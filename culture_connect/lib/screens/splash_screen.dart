import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lottie/lottie.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/providers/startup_optimization_provider.dart';
import 'package:culture_connect/services/auth_service.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/video_background.dart';
import 'onboarding_screen.dart';
import 'main_navigation.dart';
import 'verification_screen.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  bool _isInitialized = false;
  bool _animationCompleted = false;
  bool _hasNavigated = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );

    // Start the animation
    _animationController.forward();

    // Add a listener to mark animation as completed
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _animationCompleted = true;
        });
      }
    });

    // Add a timeout fallback to prevent infinite loading
    Future.delayed(const Duration(seconds: 10), () {
      if (!_hasNavigated && mounted) {
        // Force navigation to onboarding if still stuck after 10 seconds
        _navigateBasedOnAuthStatus(AuthStatus.unauthenticated);
      }
    });
  }

  void _navigateBasedOnAuthStatus(AuthStatus authStatus) {
    // Prevent multiple navigations
    if (_hasNavigated || !mounted) return;

    switch (authStatus) {
      case AuthStatus.authenticated:
        _hasNavigated = true;
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const MainNavigation()),
        );
        break;
      case AuthStatus.verificationPending:
        _hasNavigated = true;
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const VerificationScreen()),
        );
        break;
      case AuthStatus.unauthenticated:
        _hasNavigated = true;
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const OnboardingScreen()),
        );
        break;
      case AuthStatus.initial:
        // Wait for the next state update
        break;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Get initialization state
    final initState = ref.watch(appInitializationStateProvider);

    // Listen to initialization state changes
    ref.listen(appInitializationStateProvider, (previous, next) {
      if (next.isInitialized && _animationCompleted && !_isInitialized) {
        setState(() {
          _isInitialized = true;
        });
      } else if (next.error != null) {
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Initialization error: ${next.error}'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: () {
                ref
                    .read(appInitializationStateProvider.notifier)
                    .retryInitialization();
              },
            ),
          ),
        );
      }
    });

    // Listen to auth state changes when initialized
    ref.listen(authStatusProvider, (previous, next) {
      if (_animationCompleted && initState.isInitialized && !_hasNavigated) {
        next.when(
          data: (authStatus) {
            // Add a small delay to ensure UI is ready
            Future.delayed(const Duration(milliseconds: 100), () {
              if (!_hasNavigated && mounted) {
                _navigateBasedOnAuthStatus(authStatus);
              }
            });
          },
          loading: () {
            // Show loading indicator - but don't wait indefinitely
            Future.delayed(const Duration(seconds: 5), () {
              if (!_hasNavigated && mounted) {
                // Force navigation to onboarding if auth is still loading after 5 seconds
                _navigateBasedOnAuthStatus(AuthStatus.unauthenticated);
              }
            });
          },
          error: (error, stackTrace) {
            // Show error message and navigate to onboarding
            if (!_hasNavigated && mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Authentication error: $error'),
                  backgroundColor: Colors.red,
                ),
              );
              _navigateBasedOnAuthStatus(AuthStatus.unauthenticated);
            }
          },
        );
      }
    });

    return Scaffold(
      body: SplashVideoBackground(
        videoPath: 'assets/video/flashScreenVideo.mp4',
        fallbackColor: AppTheme.primaryColor,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // App logo or animation with enhanced visibility
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black
                      .withAlpha(77), // 0.3 opacity for better contrast
                  borderRadius: BorderRadius.circular(20),
                ),
                child: SizedBox(
                  width: 200,
                  height: 200,
                  child: Lottie.asset(
                    'assets/animations/splash_animation.json',
                    controller: _animationController,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // App name with enhanced visibility
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.black
                      .withAlpha(77), // 0.3 opacity for better contrast
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'CultureConnect',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 1.2,
                    shadows: [
                      Shadow(
                        offset: Offset(1, 1),
                        blurRadius: 3,
                        color: Colors.black54,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 8),

              // Tagline with enhanced visibility
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.black
                      .withAlpha(51), // 0.2 opacity for subtle contrast
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'Connect with authentic cultural experiences',
                  style: TextStyle(
                    color: Colors.white
                        .withAlpha(230), // 0.9 opacity for better readability
                    fontSize: 16,
                    shadows: const [
                      Shadow(
                        offset: Offset(1, 1),
                        blurRadius: 2,
                        color: Colors.black45,
                      ),
                    ],
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              const SizedBox(height: 32),

              // Initialization status - enhanced for video background
              if (initState.error != null)
                Container(
                  margin:
                      const EdgeInsets.symmetric(horizontal: 32, vertical: 8),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.withAlpha(179), // 0.7 opacity
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'Error: ${initState.error}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      shadows: [
                        Shadow(
                          offset: Offset(1, 1),
                          blurRadius: 2,
                          color: Colors.black45,
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

              const SizedBox(height: 16),

              // Loading indicator with progress - enhanced for video background
              Container(
                width: 200,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black
                      .withAlpha(77), // 0.3 opacity for better contrast
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    // Progress indicator
                    LinearProgressIndicator(
                      value: initState.progress,
                      backgroundColor:
                          Colors.white.withAlpha(77), // 0.3 opacity
                      valueColor:
                          const AlwaysStoppedAnimation<Color>(Colors.white),
                      borderRadius: BorderRadius.circular(8),
                    ),

                    const SizedBox(height: 8),

                    // Progress text
                    Text(
                      'Loading... ${(initState.progress * 100).toInt()}%',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        shadows: [
                          Shadow(
                            offset: Offset(1, 1),
                            blurRadius: 2,
                            color: Colors.black45,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
